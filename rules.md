# AI Agent Development & Interaction Rules

This document provides a consolidated set of rules and guidelines for AI-assisted development on this project. These rules are designed to be clear, non-redundant, and easily interpretable by Large Language Models (LLMs).

---

## 1. Core Philosophy & Principles

### 1.1. Pragmatism (DRY, KISS, YAGNI)

- **KISS (Keep It Simple, Stupid):** Always prefer the simplest possible solution that fulfills the requirement. Avoid clever tricks, complex patterns, or premature optimizations that harm readability and maintainability.
- **YAGNI (You Aren't Gonna Need It):** Do not implement any feature, abstraction, or logic unless it is required to solve a current and confirmed problem. Only add properties or code when they become necessary.
- **DRY (Don't Repeat Yourself):** Avoid duplicating logic. Abstract repeated patterns into reusable functions.

### 1.2. Focus and Scope

- **Single Responsibility:** Every function or module must have one single, well-defined responsibility. If a function performs multiple distinct operations, decompose it.
- **Separation of Concerns:** Keep unrelated concepts separate. For example, UI logic, data access, and state management must not be mixed.
- **Atomic Changes:** Only make changes that directly address the user's specific request. Do not refactor or modify unrelated code.
- **Minimal Abstraction:** Avoid introducing layers of abstraction or indirection unless they provide a clear and immediate benefit for solving the stated problem.

```typescript
// Good: Single responsibility
const calculateTax = (price: number, rate: number) => price * rate;
const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;

// Bad: Mixed responsibilities
const processPrice = (price: number, rate: number) => {
  const tax = price * rate;
  return `$${(price + tax).toFixed(2)}`; // Mixing calculation and formatting
};
```

---

## 2. Functional Programming Paradigm

This project strongly prefers a functional programming style. Do not use classes unless their use is strictly required by an external library or framework you do not control. Prefer closures and higher-order functions for all internal composition and encapsulation.

### 2.1. Purity and Immutability

- **Pure Functions:** All functions must be pure. They must not cause side effects (e.g., I/O, modifying external state) and their output must depend solely on their input arguments.
- **Immutability:** All data is immutable. Never mutate objects or arrays. Instead, return a new copy with the updated values using patterns like the spread syntax (`...`), `map`, `filter`, or `reduce`.
- **Statelessness:** Design functions to be stateless. Each operation should be independent and not rely on any previously held state (e.g., global variables, in-memory caches).
- **Idempotence:** All operations and functions must be idempotent. Executing them multiple times with the same input should always yield the same result without changing the system state beyond the initial execution.

```typescript
// Good: Pure and immutable
const addItem = (cart: Item[], newItem: Item) => [...cart, newItem];
const updateUserAge = (user: User, age: number) => ({ ...user, age });

// Bad: Impure and mutating
let globalCart: Item[] = [];
const addItemImpure = (item: Item) => {
  globalCart.push(item); // Mutates global state
  return globalCart;
};
```

### 2.2. Composition and Structure

- **Function Composition:** Build complex logic by composing small, single-purpose functions. Design functions to be easily chained or piped together. Consider currying to capture scope where appropriate.
- **Declarative Style:** Write declarative code that describes _what_ to do, not _how_ to do it.

```typescript
// Good: Functional composition
const pipe =
  <T>(...fns: Array<(arg: T) => T>) =>
  (value: T) =>
    fns.reduce((acc, fn) => fn(acc), value);

const processOrder = pipe(
  validateOrder,
  calculateTotals,
  applyDiscounts,
  formatForDisplay,
);

// Good: Declarative
const activeUsers = users.filter((user) => user.isActive);

// Bad: Imperative
const activeUsers = [];
for (let i = 0; i < users.length; i++) {
  if (users[i].isActive) {
    activeUsers.push(users[i]);
  }
}
```

---

## 3. Code Style & Conventions

### 3.1. Naming

- **Name by Behavior:** Name all functions and variables based on _what they do_ or the data they hold, not their type or implementation detail. Use clear, descriptive action verbs for functions (e.g., `calculateTotalPrice`, `fetchUserData`).

```typescript
// Good: Names describe behavior/purpose
const calculateShippingCost = (weight: number, distance: number) => ...;
const isValidEmail = (email: string) => ...;
const userCredentials = { username: 'john', password: 'secret' };

// Bad: Names describe type or implementation
const stringProcessor = (input: string) => ...;
const arrayHelper = (data: any[]) => ...;
const obj1 = { user: 'john', pass: 'secret' };
```

### 3.2. Language Features

- **Use `const`:** Always declare variables with `const` to enforce immutability.
- **Use Arrow Functions:** Default to using arrow functions for their lexical `this` binding. Only use a traditional `function` declaration if the dynamic context of `this` is explicitly required.
- **Prefer `async/await`:** Use `async/await` for all asynchronous code to maintain a linear, readable flow. Avoid raw Promises and `.then()`/`.catch()` chains unless they are clearly more readable in a specific context.
- **Use Destructuring:** Use destructuring for objects and arrays to improve readability and clarity.

### 3.3. TypeScript and Types

- **Use TypeScript:** Employ TypeScript for all new code to ensure type safety.
- **Forbid `any`:** Never use the `any` type. If a type is unknown, use `unknown` and perform type checking.
- **Keep Types Simple:** Avoid complex, convoluted, or deeply nested types, recursive generics, or heavy use of `infer`. Types must be simple and directly understandable. Prefer implicit type inference where it is clear and does not reduce readability.

```typescript
// Good: Simple, clear types
interface User {
  id: string;
  name: string;
  email: string;
}

const processUser = (user: User) => ...;

// Good: Using unknown instead of any
const parseJson = (input: string): unknown => JSON.parse(input);

// Bad: Complex, hard to understand
type DeepMagic<T, K extends keyof T> = {
  [P in K]: T[P] extends infer U
    ? U extends Function
      ? ReturnType<U>
      : never
    : never;
}[K];
```

### 3.4. Performance Considerations

- **Avoid N+1 Problems:** Be aware of iterative operations that could cascade into performance issues.
- **Memory Management:** Prefer streaming over loading large datasets into memory.
- **Bundle Size:** Consider the impact of dependencies on bundle size for client-side code.
- **Early Returns:** Use early returns and guards to avoid unnecessary computation.

```typescript
// Good: Early return prevents unnecessary work
const calculateDiscount = (user: User, order: Order) => {
  if (!user.isPremium) return 0;
  if (order.total < 100) return 0;
  return order.total * 0.1;
};

// Good: Streaming large data
const processLargeFile = async (filePath: string) => {
  const stream = createReadStream(filePath);
  return stream.pipe(new ProcessingTransform());
};

// Bad: Loading everything into memory
const processLargeFileBad = async (filePath: string) => {
  const content = await readFileSync(filePath, "utf8"); // Loads entire file
  return content.split("\n").map(processLine);
};
```

---

## 4. Error Handling

### 4.1. Error Handling Principles

- **Fail Fast:** Validate inputs early and throw meaningful errors at the point of detection.
- **Explicit Error Types:** Use custom error types that clearly indicate the failure category and context.
- **Graceful Degradation:** When possible, provide fallback behavior rather than complete failure.
- **No Silent Failures:** Always surface errors to the caller or user. Never swallow exceptions without explicit handling.
- **Predictable Patterns:** Use the prevailing error handling mechanisms of your language/framework consistently.

```typescript
// Good: Custom error types with context
class ValidationError extends Error {
  constructor(field: string, value: unknown) {
    super(`Invalid ${field}: ${value}`);
    this.name = "ValidationError";
  }
}

const validateUser = (user: unknown): User => {
  if (!user || typeof user !== "object") {
    throw new ValidationError("user", user);
  }
  // More validation...
  return user as User;
};

// Good: Graceful degradation
const getUserPreferences = async (userId: string) => {
  try {
    return await fetchUserPreferences(userId);
  } catch (error) {
    console.error("Failed to load preferences:", error);
    return getDefaultPreferences(); // Fallback
  }
};

// Bad: Silent failure
const badFunction = async (id: string) => {
  try {
    return await riskyOperation(id);
  } catch {
    return null; // Swallows error, caller doesn't know what happened
  }
};
```

---

## 5. AI Agent Behavior Patterns

### 5.1. Decision Making and Communication

- **Uncertainty Protocol:** When multiple valid approaches exist, present options with trade-offs rather than making arbitrary choices.
- **Incremental Development:** Implement the minimal viable solution first, then ask before adding enhancements.
- **Context Preservation:** Always reference existing project patterns and conventions before introducing new ones.
- **Explicit Assumptions:** State your assumptions clearly when they influence your implementation choices.

### 5.2. Code Generation Behavior

- **Justify Decisions:** Before providing code, briefly explain the reasoning for your approach and how it solves the requested problem.
- **Ask Before Enhancing:** Before implementing anything non-essential (validations, optimizations, abstractions), ask: "Is this necessary to solve the actual problem?"
- **Reference Existing Patterns:** Look for similar implementations in the codebase and follow established patterns unless explicitly asked to deviate.
- **Minimal Viable Implementation:** Start with the simplest solution that works, then iterate based on feedback.

### 5.3. Project Understanding

- **Consult Documentation:** Before starting work, look for project context and requirements in the `docs/` directory, especially `docs/context.md`.
- **Use All Input Sources:** Design tests and features based on user stories, bug reports, UI element information, and sitemaps.
- **The User is the Final Authority:** The user's instructions and decisions are final. Do not deviate without clarification and approval.

```typescript
// Example of asking before enhancing:
// "I can implement basic user creation. Should I also add:
// - Input validation beyond type checking?
// - Duplicate email prevention?
// - Password strength requirements?"

// Basic implementation first:
const createUser = (userData: CreateUserRequest): User => ({
  id: generateId(),
  ...userData,
  createdAt: new Date(),
});
```

---

## 6. E2E Testing Strategy (Playwright)

### 6.1. Test Structure and Organization

- **Organize by User Journey:** Structure tests to mirror critical user flows (e.g., `purchase-product.spec.ts`). This makes tests intuitive and ties them directly to business value.
- **Descriptive Naming:** Test names must clearly state their purpose and expected outcome (e.g., `it('allows guest to add product to cart')`).
- **Focused Test Files:** Keep test files focused on a specific feature or scenario within a user journey.

### 6.2. Test Implementation

- **Use Playwright Fixtures:** Fully leverage built-in fixtures (`page`, `context`, etc.) for setup and resource management.
- **Reusable Action Functions:** Implement test logic using pure, reusable action functions that accept the `page` fixture and other parameters (e.g., `async function fillLoginForm(page, username, password)`).
- **Independent Tests:** Tests must be idempotent and independent. They cannot rely on the state or outcome of any other test and must be runnable in any order.

### 6.3. Visual Regression Testing

- **Implement with Percy.io:** Use Percy.io for visual regression testing, integrated via Playwright.
- **Snapshot Strategy:** Capture snapshots of full pages and/or specific critical components. Use helper functions like `takePercySnapshot` and `takePercySnapshotOfElement`.
- **Handle Dynamic Content:** When taking snapshots, hide or pause dynamic content (e.g., animations, pop-ups) using CSS modifications to prevent false positives.

### 6.4. Configuration and CI/CD

- **Environment Configuration:** Support multiple environments (local, staging, production) using environment variables, with defaults in `playwright.config.ts`.
- **CI/CD Integration:** Ensure the test suite is designed for seamless integration with GitLab CI, running within a Docker container. Store sensitive tokens securely in CI variables.

---

## 7. Agent & Rule Portability

### 7.1. LLM Portability and Context Generation

- **LLM Portability:** All rules and structures should be defined in a generic way that can be understood by different AI agents with minimal friction.
- **Context Generation:** Follow a structured format (like the one in `dotfiles/llm-rules/universal_context_prompt.md`) when generating project context for other agents.

---

## 8. General Best Practices

### 8.1. Documentation and Comments

- **Inline Documentation:** Document public-facing functions inline using standard comment conventions.
- **Intentional Comments:** Do not write comments that explain _what_ the code is doing. If a comment is necessary for a complex piece of logic, it should explain the _intention_ or _why_.

### 8.2. Reliability

- **Logging:** Add structured logging for key events: function entry/exit points, important state changes, and error conditions. Never log sensitive data.
- **Testability:** Write code that is easy to test. Avoid hidden dependencies; use dependency injection or explicit function parameters.

```typescript
// Good: Structured logging without sensitive data
const processPayment = async (paymentData: PaymentRequest) => {
  logger.info("Processing payment", {
    orderId: paymentData.orderId,
    amount: paymentData.amount,
    // Never log: credit card numbers, passwords, etc.
  });

  try {
    const result = await paymentService.process(paymentData);
    logger.info("Payment processed successfully", {
      orderId: paymentData.orderId,
    });
    return result;
  } catch (error) {
    logger.error("Payment processing failed", {
      orderId: paymentData.orderId,
      error: error.message,
    });
    throw error;
  }
};

// Good: Testable with dependency injection
const createOrderService = (
  database: Database,
  emailService: EmailService,
) => ({
  createOrder: async (orderData: OrderData) => {
    // Implementation uses injected dependencies
  },
});
```

### 8.3. Maintenance

- **Update Imports:** When renaming a file or an export, ensure all import statements referencing the old name are updated accordingly.

### 8.4. Project Structure & Dependencies

- **Feature-Based Directory Structure:** Organize files and directories by feature or domain concept, not by file type. Co-locate all code related to a single feature (e.g., types, API calls, tests, and helpers) within the same directory.
- **Minimal and Vetted Dependencies:** Introduce new third-party dependencies sparingly and only after careful consideration. Prefer libraries that are well-maintained, have a small footprint, and align with the project's functional principles.
